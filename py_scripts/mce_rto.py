import requests
from config import <PERSON><PERSON><PERSON><PERSON>OMAIN, MCE_CLIENT_ID, MC<PERSON>_CLIENT_SECRET, MCE_ACCOUNT_ID, MCE_AUTH_URL, MC<PERSON>_SEND_SMS_URL, PGSQL_USER, PGSQL_PASSWORD, PGSQL_HOST, PGSQL_DATABASE
from datetime import datetime
import pytz
import psycopg2
import os
import sys
import fcntl
import atexit
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

# Global variable to store the lock file handle
lock_file = None

def check_if_already_running():
    """Check if another instance of this script is already running."""
    global lock_file

    # Get the script name without extension for the lock file
    script_name = os.path.splitext(os.path.basename(__file__))[0]
    lock_file_path = f"/tmp/{script_name}.lock"

    try:
        # Try to open and lock the file
        lock_file = open(lock_file_path, 'w')
        fcntl.flock(lock_file.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)

        # Write the current process ID to the lock file
        lock_file.write(str(os.getpid()))
        lock_file.flush()

        # Register cleanup function to remove lock file on exit
        atexit.register(cleanup_lock_file)

        return False  # Not already running

    except (IOError, OSError):
        # Another instance is already running
        if lock_file:
            lock_file.close()
        return True  # Already running

def cleanup_lock_file():
    """Clean up the lock file when the script exits."""
    global lock_file
    if lock_file:
        try:
            fcntl.flock(lock_file.fileno(), fcntl.LOCK_UN)
            lock_file.close()
            script_name = os.path.splitext(os.path.basename(__file__))[0]
            lock_file_path = f"/tmp/{script_name}.lock"
            if os.path.exists(lock_file_path):
                os.remove(lock_file_path)
        except:
            pass  # Ignore errors during cleanup

domain = MCE_DOMAIN
client_id = MCE_CLIENT_ID
client_secret = MCE_CLIENT_SECRET
account_id = MCE_ACCOUNT_ID
auth_url = MCE_AUTH_URL
send_sms_url = MCE_SEND_SMS_URL
sms_send_id = 'MjA5ODg6Nzg6MA'

# Configuration for batch processing and threading
BATCH_SIZE = 50  # Number of records to process in each batch
MAX_THREADS = 5  # Maximum number of concurrent threads
THREAD_DELAY = 0.1  # Delay between thread starts to avoid overwhelming the API

def authenticate(client_id, client_secret):
    payload = {
        'grant_type': 'client_credentials',
        'client_id': client_id,
        'client_secret': client_secret,
        'account_id': account_id
    }
    
    response = requests.post(auth_url, json=payload)
    if response.status_code != 200:
        print("Authentication Error:", response.text)
        raise Exception("Failed to authenticate with Salesforce Marketing Cloud API")
    
    return response.json().get('access_token')

def send_sms(access_token, phone_number, message):
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }
    
    data = {
        "Subscribe": True,
        "Resubscribe": True,
        "mobileNumbers": [phone_number],
        "keyword": "NIC",
        "Override": True,
        "messageText": message
    }
    
    url = send_sms_url.format(sms_send_id=sms_send_id)
    response = requests.post(url, headers=headers, json=data)
    
    if response.status_code == 202:
        return {"status": "success", "message": f"{response.status_code} - {response.text}"}
    else:
        return {"status": "error", "message": f"{response.status_code} - {response.text}"}

def get_sms_data_from_db():
    """Legacy function - kept for backward compatibility. Use get_sms_batch_from_db for new implementations."""
    connection = None
    cursor = None
    try:
        connection = psycopg2.connect(user=PGSQL_USER, password=PGSQL_PASSWORD, host=PGSQL_HOST, database=PGSQL_DATABASE)
        cursor = connection.cursor()

        query = """
            SELECT id_sms, phone, email, firstname, lastname, version, message
            FROM subscriber.transaction_sms
            WHERE platform = 'mce_rto' AND campaign_date = CURRENT_DATE AND delivery_status IS NULL
        """
        cursor.execute(query)
        records = cursor.fetchall()

        sms_data = []
        for record in records:
            id_sms, phone, email, firstname, lastname, version, message = record
            sms_data.append({
                'id_sms': id_sms,
                'phone': phone,
                'email': email,
                'firstname': firstname,
                'lastname': lastname,
                'version': version,
                'message': message
            })
        return sms_data

    except Exception as e:
        print("Error reading data from PostgreSQL table:", str(e))
        return []  # Return empty list instead of None
    finally:
        if cursor is not None:
            cursor.close()
        if connection is not None:
            connection.close()

def get_sms_batch_from_db(batch_size=BATCH_SIZE, offset=0):
    """Get a batch of SMS data from the database with pagination."""
    connection = None
    cursor = None
    try:
        connection = psycopg2.connect(user=PGSQL_USER, password=PGSQL_PASSWORD, host=PGSQL_HOST, database=PGSQL_DATABASE)
        cursor = connection.cursor()

        query = """
            SELECT id_sms, phone, email, firstname, lastname, version, message
            FROM subscriber.transaction_sms
            WHERE platform = 'mce_rto' AND campaign_date = CURRENT_DATE AND delivery_status IS NULL
            ORDER BY id_sms
            LIMIT %s OFFSET %s
        """
        cursor.execute(query, (batch_size, offset))
        records = cursor.fetchall()

        sms_data = []
        for record in records:
            id_sms, phone, email, firstname, lastname, version, message = record
            sms_data.append({
                'id_sms': id_sms,
                'phone': phone,
                'email': email,
                'firstname': firstname,
                'lastname': lastname,
                'version': version,
                'message': message
            })
        return sms_data

    except Exception as e:
        print(f"Error reading batch data from PostgreSQL table (offset {offset}): {str(e)}")
        return []  # Return empty list instead of None
    finally:
        if cursor is not None:
            cursor.close()
        if connection is not None:
            connection.close()

def process_sms_item(access_token, sms_item):
    """Process a single SMS item. Thread-safe function."""
    try:
        id_sms = sms_item['id_sms']
        phone = sms_item['phone']
        message = sms_item['message']

        # Add 1 prefix to phone number
        formatted_phone = f"1{phone}"
        result = send_sms(access_token, formatted_phone, message)

        status = result['status']
        response_mce = result['message']
        delivery_date = datetime.now().replace(tzinfo=pytz.utc).astimezone(pytz.timezone('US/Pacific')) if status == "success" else None

        # Update delivery status in database
        update_delivery_status(id_sms, status, delivery_date, response_mce)

        return {
            'id_sms': id_sms,
            'status': status,
            'success': status == 'success'
        }

    except Exception as e:
        print(f"Error processing SMS {sms_item.get('id_sms', 'unknown')}: {str(e)}")
        return {
            'id_sms': sms_item.get('id_sms', 'unknown'),
            'status': 'error',
            'success': False,
            'error': str(e)
        }

def process_sms_batch(access_token, sms_batch, thread_id):
    """Process a batch of SMS items in a single thread."""
    print(f"Thread {thread_id}: Processing batch of {len(sms_batch)} SMS items")
    results = []

    for sms_item in sms_batch:
        result = process_sms_item(access_token, sms_item)
        results.append(result)

        # Small delay to avoid overwhelming the API
        time.sleep(0.05)  # 50ms delay between individual SMS sends

    success_count = sum(1 for r in results if r['success'])
    print(f"Thread {thread_id}: Completed batch - {success_count}/{len(sms_batch)} successful")

    return results

def update_delivery_status(id_sms, status, delivery_date, response_mce):
    connection = None
    cursor = None
    try:
        connection = psycopg2.connect(user=PGSQL_USER, password=PGSQL_PASSWORD, host=PGSQL_HOST, database=PGSQL_DATABASE)
        cursor = connection.cursor()

        update_query = """
            UPDATE subscriber.transaction_sms
            SET delivery_status = %s, delivery_date = %s, response_mce = %s
            WHERE id_sms = %s
        """
        cursor.execute(update_query, (status, delivery_date, response_mce, id_sms))
        connection.commit()
    except Exception as e:
        print("Error updating delivery status in PostgreSQL table:", str(e))
    finally:
        if cursor is not None:
            cursor.close()
        if connection is not None:
            connection.close()

def main():
    # Check if another instance is already running
    if check_if_already_running():
        print("Another instance of this script is already running. Exiting...")
        sys.exit(1)

    print("Starting SMS processing with batch and multi-threading...")
    print(f"Configuration: Batch size={BATCH_SIZE}, Max threads={MAX_THREADS}")

    # Authenticate once for all threads
    access_token = authenticate(client_id, client_secret)

    # Initialize counters
    total_processed = 0
    total_successful = 0
    batch_number = 0
    offset = 0

    # Process data in batches until no more data
    while True:
        batch_number += 1
        print(f"\n--- Processing Batch {batch_number} (offset: {offset}) ---")

        # Get the next batch of data
        sms_batch = get_sms_batch_from_db(BATCH_SIZE, offset)

        # If no more data, break the loop
        if not sms_batch:
            print("No more data to process. Finishing...")
            break

        print(f"Retrieved {len(sms_batch)} SMS records for batch {batch_number}")

        # Split the batch into smaller chunks for threading
        chunk_size = max(1, len(sms_batch) // MAX_THREADS)
        chunks = [sms_batch[i:i + chunk_size] for i in range(0, len(sms_batch), chunk_size)]

        # Process chunks using ThreadPoolExecutor
        batch_results = []
        with ThreadPoolExecutor(max_workers=MAX_THREADS) as executor:
            # Submit all chunks to the thread pool
            future_to_thread = {}
            for i, chunk in enumerate(chunks):
                if chunk:  # Only submit non-empty chunks
                    thread_id = f"{batch_number}-{i+1}"
                    future = executor.submit(process_sms_batch, access_token, chunk, thread_id)
                    future_to_thread[future] = thread_id

                    # Small delay between thread starts
                    time.sleep(THREAD_DELAY)

            # Collect results as they complete
            for future in as_completed(future_to_thread):
                thread_id = future_to_thread[future]
                try:
                    results = future.result()
                    batch_results.extend(results)
                except Exception as e:
                    print(f"Thread {thread_id} generated an exception: {e}")

        # Calculate batch statistics
        batch_successful = sum(1 for r in batch_results if r['success'])
        total_processed += len(batch_results)
        total_successful += batch_successful

        print(f"Batch {batch_number} completed: {batch_successful}/{len(batch_results)} successful")

        # Move to next batch
        offset += BATCH_SIZE

        # Small delay between batches to avoid overwhelming the system
        time.sleep(0.5)

    # Final summary
    print(f"\n=== Processing Complete ===")
    print(f"Total SMS processed: {total_processed}")
    print(f"Total successful: {total_successful}")
    print(f"Total failed: {total_processed - total_successful}")
    print(f"Success rate: {(total_successful/total_processed*100):.1f}%" if total_processed > 0 else "No data processed")

if __name__ == "__main__":
    main()
